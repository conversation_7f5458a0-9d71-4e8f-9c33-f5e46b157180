export interface AppSettings {
  llm: LLMProviderConfig;
  ui: UISettings;
  agent: AgentSettings;
  tools: ToolSettings;
}

export interface LLMProviderConfig {
  provider: 'openai' | 'anthropic' | 'deepseek';
  model: string;
  apiKey: string;
  baseUrl?: string;
  maxTokens: number;
  temperature: number;
}

export interface UISettings {
  theme: 'system' | 'light' | 'dark';
  fontSize: 'small' | 'medium' | 'large';
  compactMode: boolean;
}

export interface AgentSettings {
  defaultExecutionMode: 'confirm' | 'yolo';
  autoSaveConversations: boolean;
  maxContextLength: number;
}

export interface ToolSettings {
  enabledTools: string[];
  toolSettings: Record<string, any>;
}

export interface Conversation {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, any>;
  status?: 'sending' | 'sent' | 'error';
  error?: string;
  tokens?: number;
  model?: string;
}

export interface AgentState {
  isWaitingForConfirmation?: boolean;
  currentTask?: string;
  status: 'idle' | 'thinking' | 'executing' | 'waiting';
  lastUpdate: Date;
}

export interface AgentPlan {
  id: string;
  description: string;
  steps: PlanStep[];
  riskLevel: 'low' | 'medium' | 'high';
  warnings?: string[];
  estimatedDuration?: string;
  createdAt: Date;
}

export interface PlanStep {
  id: string;
  description: string;
  action: string;
  type: 'tool' | 'code' | 'file' | 'system';
  details?: string;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface ToolResult {
  id: string;
  toolName: string;
  input: any;
  output: any;
  status: 'success' | 'error' | 'running';
  error?: string;
  duration?: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface SystemInfo {
  platform: string;
  arch: string;
  nodeVersion: string;
  electronVersion: string;
  appVersion: string;
  memory: {
    total: number;
    used: number;
    available: number;
  };
  cpu: {
    model: string;
    cores: number;
    speed: number;
  };
  network: Record<string, NetworkInterfaceInfo[]>;
}

export interface NetworkInterfaceInfo {
  address: string;
  netmask: string;
  family: string;
  mac: string;
  internal: boolean;
  cidr?: string | null;
  scopeid?: number;
}

export interface FileSystemWatcher {
  path: string;
  recursive?: boolean;
  ignored?: string[];
}

export interface DatabaseStats {
  conversations: number;
  messages: number;
  size: number;
  lastBackup?: Date;
}

// Database Entity Types
export interface DBConversation {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  metadata: string; // JSON string
}

export interface DBMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  metadata: string; // JSON string
}

export interface DBSettings {
  key: string;
  value: string; // JSON string
  updated_at: string;
}

// Event Types for IPC communication
export interface IPCEvents {
  // Settings
  'settings:get': () => Promise<AppSettings>;
  'settings:update': (updates: Partial<AppSettings>) => Promise<void>;
  'settings:reset': () => Promise<void>;

  // Conversations
  'conversations:list': () => Promise<Conversation[]>;
  'conversations:get': (id: string) => Promise<Conversation | null>;
  'conversations:create': (conversation: Omit<Conversation, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Conversation>;
  'conversations:update': (id: string, updates: Partial<Conversation>) => Promise<void>;
  'conversations:delete': (id: string) => Promise<void>;

  // Messages
  'messages:add': (conversationId: string, message: Omit<Message, 'id' | 'timestamp'>) => Promise<Message>;
  'messages:update': (conversationId: string, messageId: string, updates: Partial<Message>) => Promise<void>;

  // Agent
  'agent:send-message': (conversationId: string, message: string) => Promise<void>;
  'agent:stop': () => Promise<void>;
  'agent:confirm-plan': (planId: string) => Promise<void>;
  'agent:reject-plan': (planId: string) => Promise<void>;

  // System
  'system:info': () => Promise<SystemInfo>;
  'system:logs': (level?: string, limit?: number) => Promise<LogEntry[]>;

  // File System
  'fs:read-file': (path: string) => Promise<string>;
  'fs:write-file': (path: string, content: string) => Promise<void>;
  'fs:list-directory': (path: string) => Promise<FileEntry[]>;

  // Window Controls
  'window:minimize': () => void;
  'window:maximize': () => void;
  'window:restore': () => void;
  'window:close': () => void;
  'window:is-maximized': () => boolean;
}

export interface LogEntry {
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: Date;
  source?: string;
  metadata?: Record<string, any>;
}

export interface FileEntry {
  name: string;
  path: string;
  isDirectory: boolean;
  size?: number;
  modifiedAt?: Date;
}

// LLM Provider Types
export interface LLMResponse {
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: 'stop' | 'length' | 'content_filter' | 'tool_calls';
  metadata?: Record<string, any>;
}

export interface StreamingLLMResponse {
  id: string;
  content: string;
  delta: string;
  done: boolean;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason?: 'stop' | 'length' | 'content_filter' | 'tool_calls';
  metadata?: Record<string, any>;
}

// Tool System Types
export interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
  description?: string;
}

export interface ToolSchema {
  name: string;
  description: string;
  category: string;
  parameters: {
    type: 'object';
    properties: Record<string, {
      type: string;
      description: string;
      enum?: string[];
      default?: any;
      required?: boolean;
    }>;
    required: string[];
  };
  examples?: Array<{
    description: string;
    arguments: Record<string, any>;
    expectedOutput?: string;
  }>;
  riskLevel: 'low' | 'medium' | 'high';
  requiresConfirmation?: boolean;
}

export interface ToolExecutionContext {
  workingDirectory?: string;
  environment?: Record<string, string>;
  timeout?: number;
  user?: {
    id: string;
    permissions: string[];
  };
}

// API Communication Types
export interface APIRequest<T = any> {
  id?: string;
  method: string;
  data?: T;
  metadata?: Record<string, any>;
  timestamp?: Date;
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  metadata?: Record<string, any>;
  timestamp?: Date;
}

// Enhanced Agent State
export interface AgentState {
  status: 'idle' | 'thinking' | 'planning' | 'executing' | 'waiting' | 'error';
  currentTask?: string;
  currentStep?: number;
  totalSteps?: number;
  isWaitingForConfirmation?: boolean;
  executionMode?: 'confirm' | 'yolo';
  lastUpdate: Date;
  toolResults: ToolResult[];
  error?: string;
  progress?: {
    current: number;
    total: number;
    description: string;
  };
}

// Process Management Types
export interface ProcessInfo {
  pid: number;
  name: string;
  command: string;
  status: 'running' | 'stopped' | 'zombie' | 'sleeping';
  cpu?: number;
  memory?: number;
  startTime?: Date;
  user?: string;
}

export interface ShellCommandOptions {
  cwd?: string;
  env?: Record<string, string>;
  timeout?: number;
  shell?: string;
  encoding?: string;
  maxBuffer?: number;
  killSignal?: string;
  uid?: number;
  gid?: number;
  windowsHide?: boolean;
}

export interface ShellCommandResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  signal?: string;
  killed: boolean;
  duration: number;
  command: string;
  pid?: number;
}

// File System Types
export interface FileStats {
  size: number;
  isFile: boolean;
  isDirectory: boolean;
  isSymbolicLink: boolean;
  mode: number;
  uid: number;
  gid: number;
  atime: Date;
  mtime: Date;
  ctime: Date;
  birthtime: Date;
}

export interface GlobOptions {
  cwd?: string;
  ignore?: string[];
  dot?: boolean;
  absolute?: boolean;
  followSymlinks?: boolean;
  maxDepth?: number;
}

export interface GrepOptions {
  ignoreCase?: boolean;
  wholeWord?: boolean;
  lineNumber?: boolean;
  context?: number;
  maxMatches?: number;
  include?: string[];
  exclude?: string[];
  recursive?: boolean;
}

export interface GrepResult {
  file: string;
  line: number;
  column: number;
  match: string;
  context?: {
    before: string[];
    after: string[];
  };
}

// Diff and Code Review Types
export interface DiffLine {
  type: 'add' | 'remove' | 'context' | 'header';
  content: string;
  lineNumber?: {
    old?: number;
    new?: number;
  };
}

export interface FileDiff {
  filename: string;
  oldFilename?: string;
  status: 'added' | 'deleted' | 'modified' | 'renamed';
  lines: DiffLine[];
  stats: {
    additions: number;
    deletions: number;
    changes: number;
  };
}

// Toast and Notification Types
export interface Toast {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message?: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
    style?: 'primary' | 'secondary' | 'danger';
  }>;
  timestamp: Date;
}

// Streaming and Real-time Types
export interface StreamingState {
  isStreaming: boolean;
  streamId?: string;
  content: string;
  metadata?: Record<string, any>;
}

export interface ProgressState {
  isActive: boolean;
  current: number;
  total: number;
  description: string;
  startTime?: Date;
  estimatedCompletion?: Date;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type EventCallback<T = any> = (data: T) => void;
export type UnsubscribeFn = () => void;