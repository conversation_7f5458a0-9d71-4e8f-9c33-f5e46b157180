import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import type { ToolSchema } from '@shared/types';
import { TOOL_CATEGORIES } from '@shared/constants';

const execAsync = promisify(exec);

export const category = TOOL_CATEGORIES.SYSTEM;

export const schema: ToolSchema = {
  name: 'run_shell_command',
  description: 'Execute a shell command and return its output. Use this for system operations, running scripts, or interacting with command-line tools.',
  category: TOOL_CATEGORIES.SYSTEM,
  parameters: {
    type: 'object',
    properties: {
      command: {
        type: 'string',
        description: 'The shell command to execute',
      },
      workingDirectory: {
        type: 'string',
        description: 'Working directory for the command (optional)',
      },
      timeout: {
        type: 'number',
        description: 'Command timeout in milliseconds (default: 30000)',
        default: 30000,
      },
      shell: {
        type: 'string',
        description: 'Shell to use for command execution (defaults to system shell)',
      },
    },
    required: ['command'],
  },
  examples: [
    {
      description: 'List files in current directory',
      arguments: { command: 'ls -la' },
      expectedOutput: 'Directory listing with file details',
    },
    {
      description: 'Check Node.js version',
      arguments: { command: 'node --version' },
      expectedOutput: 'Node.js version number',
    },
  ],
  riskLevel: 'high',
  requiresConfirmation: true,
};

export async function execute(args: {
  command: string;
  workingDirectory?: string;
  timeout?: number;
  shell?: string;
}): Promise<{
  stdout: string;
  stderr: string;
  exitCode: number;
  command: string;
  executionTime: number;
}> {
  const { command, workingDirectory, timeout = 30000, shell } = args;
  const startTime = Date.now();

  try {
    const options: any = {
      cwd: workingDirectory || process.cwd(),
      timeout,
      killSignal: 'SIGTERM',
      maxBuffer: 1024 * 1024 * 10, // 10MB buffer
    };

    if (shell) {
      options.shell = shell;
    }

    const { stdout, stderr } = await execAsync(command, options);
    
    return {
      stdout: stdout.toString(),
      stderr: stderr.toString(),
      exitCode: 0,
      command,
      executionTime: Date.now() - startTime,
    };
  } catch (error: any) {
    return {
      stdout: error.stdout?.toString() || '',
      stderr: error.stderr?.toString() || error.message,
      exitCode: error.code || 1,
      command,
      executionTime: Date.now() - startTime,
    };
  }
}

// Enhanced version with streaming support
export async function executeStreaming(args: {
  command: string;
  workingDirectory?: string;
  shell?: string;
  onStdout?: (data: string) => void;
  onStderr?: (data: string) => void;
}): Promise<{
  exitCode: number;
  command: string;
  executionTime: number;
}> {
  const { command, workingDirectory, shell, onStdout, onStderr } = args;
  const startTime = Date.now();

  return new Promise((resolve, reject) => {
    const options: any = {
      cwd: workingDirectory || process.cwd(),
      shell: shell || true,
    };

    const child = spawn(command, [], options);
    let exitCode = 0;

    child.stdout?.on('data', (data: Buffer) => {
      if (onStdout) {
        onStdout(data.toString());
      }
    });

    child.stderr?.on('data', (data: Buffer) => {
      if (onStderr) {
        onStderr(data.toString());
      }
    });

    child.on('error', (error) => {
      reject(error);
    });

    child.on('close', (code) => {
      exitCode = code || 0;
      resolve({
        exitCode,
        command,
        executionTime: Date.now() - startTime,
      });
    });

    // Set a reasonable timeout for streaming commands
    setTimeout(() => {
      if (!child.killed) {
        child.kill('SIGTERM');
        reject(new Error(`Command timed out: ${command}`));
      }
    }, 60000); // 60 seconds
  });
}