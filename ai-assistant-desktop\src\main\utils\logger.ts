import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  stack?: string;
}

class Logger {
  private logDir: string;
  private logFile: string;
  private maxLogSize = 10 * 1024 * 1024; // 10MB
  private maxLogFiles = 5;

  constructor() {
    this.logDir = path.join(app.getPath('userData'), 'logs');
    this.logFile = path.join(this.logDir, 'app.log');
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  private formatLogEntry(level: LogLevel, message: string, data?: any, stack?: string): string {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      data,
      stack,
    };

    return JSON.stringify(entry) + '\n';
  }

  private writeToFile(content: string): void {
    try {
      // Check if log rotation is needed
      if (fs.existsSync(this.logFile)) {
        const stats = fs.statSync(this.logFile);
        if (stats.size > this.maxLogSize) {
          this.rotateLogFiles();
        }
      }

      fs.appendFileSync(this.logFile, content);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  private rotateLogFiles(): void {
    try {
      // Move existing log files
      for (let i = this.maxLogFiles - 1; i > 0; i--) {
        const oldFile = path.join(this.logDir, `app.log.${i}`);
        const newFile = path.join(this.logDir, `app.log.${i + 1}`);
        
        if (fs.existsSync(oldFile)) {
          if (i === this.maxLogFiles - 1) {
            fs.unlinkSync(oldFile); // Delete oldest log
          } else {
            fs.renameSync(oldFile, newFile);
          }
        }
      }

      // Move current log to .1
      if (fs.existsSync(this.logFile)) {
        fs.renameSync(this.logFile, path.join(this.logDir, 'app.log.1'));
      }
    } catch (error) {
      console.error('Failed to rotate log files:', error);
    }
  }

  debug(message: string, data?: any): void {
    const logEntry = this.formatLogEntry('debug', message, data);
    console.debug(`[DEBUG] ${message}`, data);
    this.writeToFile(logEntry);
  }

  info(message: string, data?: any): void {
    const logEntry = this.formatLogEntry('info', message, data);
    console.info(`[INFO] ${message}`, data);
    this.writeToFile(logEntry);
  }

  warn(message: string, data?: any): void {
    const logEntry = this.formatLogEntry('warn', message, data);
    console.warn(`[WARN] ${message}`, data);
    this.writeToFile(logEntry);
  }

  error(message: string, error?: Error | any): void {
    const stack = error instanceof Error ? error.stack : undefined;
    const data = error instanceof Error ? { name: error.name, message: error.message } : error;
    
    const logEntry = this.formatLogEntry('error', message, data, stack);
    console.error(`[ERROR] ${message}`, error);
    this.writeToFile(logEntry);
  }

  // Get recent log entries
  getRecentLogs(count: number = 100): LogEntry[] {
    try {
      if (!fs.existsSync(this.logFile)) {
        return [];
      }

      const content = fs.readFileSync(this.logFile, 'utf-8');
      const lines = content.trim().split('\n').filter(line => line.trim());
      
      return lines
        .slice(-count)
        .map(line => {
          try {
            return JSON.parse(line) as LogEntry;
          } catch {
            return {
              timestamp: new Date().toISOString(),
              level: 'info' as LogLevel,
              message: line,
            };
          }
        });
    } catch (error) {
      console.error('Failed to read log file:', error);
      return [];
    }
  }

  // Clear all logs
  clearLogs(): void {
    try {
      if (fs.existsSync(this.logFile)) {
        fs.unlinkSync(this.logFile);
      }

      // Remove rotated logs
      for (let i = 1; i <= this.maxLogFiles; i++) {
        const logFile = path.join(this.logDir, `app.log.${i}`);
        if (fs.existsSync(logFile)) {
          fs.unlinkSync(logFile);
        }
      }
    } catch (error) {
      console.error('Failed to clear logs:', error);
    }
  }

  // Get log file paths
  getLogFiles(): string[] {
    try {
      const files = fs.readdirSync(this.logDir)
        .filter(file => file.startsWith('app.log'))
        .map(file => path.join(this.logDir, file))
        .sort((a, b) => {
          const aStats = fs.statSync(a);
          const bStats = fs.statSync(b);
          return bStats.mtime.getTime() - aStats.mtime.getTime();
        });

      return files;
    } catch (error) {
      console.error('Failed to get log files:', error);
      return [];
    }
  }

  // Get log directory size
  getLogDirectorySize(): number {
    try {
      let totalSize = 0;
      const files = fs.readdirSync(this.logDir);
      
      for (const file of files) {
        const filePath = path.join(this.logDir, file);
        const stats = fs.statSync(filePath);
        totalSize += stats.size;
      }

      return totalSize;
    } catch (error) {
      console.error('Failed to get log directory size:', error);
      return 0;
    }
  }
}

// Create singleton instance
export const logger = new Logger();

// Error handling utilities
export class AppError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    statusCode: number = 500,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Common error types
export class ValidationError extends AppError {
  constructor(message: string, field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} not found`, 'NOT_FOUND', 404);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 'UNAUTHORIZED', 401);
    this.name = 'UnauthorizedError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_EXCEEDED', 429);
    this.name = 'RateLimitError';
  }
}

export class NetworkError extends AppError {
  constructor(message: string = 'Network error') {
    super(message, 'NETWORK_ERROR', 503);
    this.name = 'NetworkError';
  }
}

// Error handler function
export const handleError = (error: Error | AppError, context?: string): void => {
  const contextMessage = context ? `[${context}] ` : '';
  
  if (error instanceof AppError) {
    if (error.isOperational) {
      logger.warn(`${contextMessage}Operational error: ${error.message}`, {
        code: error.code,
        statusCode: error.statusCode,
        stack: error.stack,
      });
    } else {
      logger.error(`${contextMessage}Programming error: ${error.message}`, error);
    }
  } else {
    logger.error(`${contextMessage}Unexpected error: ${error.message}`, error);
  }
};

// Async error wrapper
export const asyncErrorHandler = <T extends any[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      handleError(error as Error, fn.name);
      throw error;
    }
  };
};

// Performance monitoring
export class PerformanceMonitor {
  private static timers = new Map<string, number>();

  static start(label: string): void {
    this.timers.set(label, Date.now());
  }

  static end(label: string): number {
    const startTime = this.timers.get(label);
    if (!startTime) {
      logger.warn(`Performance timer '${label}' was not started`);
      return 0;
    }

    const duration = Date.now() - startTime;
    this.timers.delete(label);
    
    logger.debug(`Performance: ${label} took ${duration}ms`);
    return duration;
  }

  static measure<T>(label: string, fn: () => T): T {
    this.start(label);
    try {
      const result = fn();
      this.end(label);
      return result;
    } catch (error) {
      this.end(label);
      throw error;
    }
  }

  static async measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.start(label);
    try {
      const result = await fn();
      this.end(label);
      return result;
    } catch (error) {
      this.end(label);
      throw error;
    }
  }
}
