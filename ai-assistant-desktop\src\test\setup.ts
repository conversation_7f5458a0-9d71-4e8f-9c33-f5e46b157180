import '@testing-library/jest-dom';

// Mock Electron APIs
const mockElectronAPI = {
  // IPC methods
  invoke: jest.fn(),
  on: jest.fn(),
  removeAllListeners: jest.fn(),
  
  // Settings API
  settings: {
    get: jest.fn(),
    set: jest.fn(),
    reset: jest.fn(),
  },
  
  // Conversation API
  conversations: {
    getAll: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    getMessages: jest.fn(),
    addMessage: jest.fn(),
  },
  
  // Agent API
  agent: {
    processMessage: jest.fn(),
    createPlan: jest.fn(),
    confirmPlan: jest.fn(),
    executePlan: jest.fn(),
    getState: jest.fn(),
    reset: jest.fn(),
  },
  
  // System API
  system: {
    getInfo: jest.fn(),
    toggleDevTools: jest.fn(),
    minimize: jest.fn(),
    maximize: jest.fn(),
    close: jest.fn(),
  },
  
  // File API
  files: {
    read: jest.fn(),
    write: jest.fn(),
    exists: jest.fn(),
    delete: jest.fn(),
    list: jest.fn(),
  },
  
  // Shell API
  shell: {
    execute: jest.fn(),
    openExternal: jest.fn(),
    showItemInFolder: jest.fn(),
  },
};

// Mock window.electronAPI
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
});

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollIntoView
Element.prototype.scrollIntoView = jest.fn();

// Mock clipboard API
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: jest.fn().mockResolvedValue(undefined),
    readText: jest.fn().mockResolvedValue(''),
  },
  writable: true,
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// Mock fetch
global.fetch = jest.fn();

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is deprecated') ||
        args[0].includes('Warning: componentWillReceiveProps has been renamed'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };

  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('componentWillReceiveProps has been renamed')
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Global test utilities
export const createMockConversation = (overrides = {}) => ({
  id: 'test-conversation-id',
  title: 'Test Conversation',
  messages: [],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockMessage = (overrides = {}) => ({
  id: 'test-message-id',
  role: 'user' as const,
  content: 'Test message',
  timestamp: new Date().toISOString(),
  ...overrides,
});

export const createMockSettings = (overrides = {}) => ({
  llm: {
    provider: 'openai',
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2048,
    topP: 1.0,
    frequencyPenalty: 0.0,
    presencePenalty: 0.0,
    apiKeys: {
      openai: 'test-key',
      anthropic: '',
      deepseek: '',
    },
  },
  ui: {
    theme: 'dark',
    sidebarOpen: true,
    fontSize: 'medium',
    fontFamily: 'Inter',
    compactMode: false,
    showLineNumbers: true,
    wordWrap: true,
    animations: true,
  },
  agent: {
    defaultExecutionMode: 'confirm',
    autoSave: true,
    confirmDestructiveActions: true,
    maxConcurrentTools: 3,
    toolTimeout: 30000,
    enableLogging: true,
    logLevel: 'info',
  },
  system: {
    autoUpdate: true,
    telemetry: false,
    crashReporting: true,
    hardwareAcceleration: true,
    startMinimized: false,
    minimizeToTray: true,
    closeToTray: false,
  },
  shortcuts: {
    newConversation: 'Ctrl+N',
    toggleSidebar: 'Ctrl+B',
    toggleSettings: 'Ctrl+,',
    focusInput: 'Ctrl+Enter',
    closeModal: 'Escape',
  },
  advanced: {
    debugMode: false,
    experimentalFeatures: false,
    customPrompts: [],
    toolWhitelist: [],
    toolBlacklist: [],
    maxHistorySize: 1000,
    contextWindow: 4096,
  },
  ...overrides,
});

export const createMockAgentPlan = (overrides = {}) => ({
  id: 'test-plan-id',
  title: 'Test Plan',
  description: 'A test plan',
  requires_confirmation: true,
  steps: [
    {
      id: 'test-step-id',
      title: 'Test Step',
      description: 'A test step',
      tool: 'write_file',
      parameters: {
        path: 'test.txt',
        content: 'test content',
      },
    },
  ],
  ...overrides,
});

export const createMockToolResult = (overrides = {}) => ({
  success: true,
  output: 'Tool executed successfully',
  data: {},
  ...overrides,
});

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks();
  localStorageMock.clear();
  sessionStorageMock.clear();
});
