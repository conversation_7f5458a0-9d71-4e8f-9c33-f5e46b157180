import * as fs from 'fs/promises';
import * as path from 'path';
import { createReadStream } from 'fs';
import { createInterface } from 'readline';
import type { ToolSchema } from '@shared/types';
import { TOOL_CATEGORIES } from '@shared/constants';

export const category = TOOL_CATEGORIES.SEARCH;

export const schema: ToolSchema = {
  name: 'grep',
  description: 'Search for patterns in files using regular expressions. Returns matching lines with context.',
  category: TOOL_CATEGORIES.SEARCH,
  parameters: {
    type: 'object',
    properties: {
      pattern: {
        type: 'string',
        description: 'The regular expression pattern to search for',
      },
      path: {
        type: 'string',
        description: 'File or directory path to search in',
      },
      recursive: {
        type: 'boolean',
        description: 'Search recursively in directories (default: false)',
        default: false,
      },
      caseSensitive: {
        type: 'boolean',
        description: 'Case-sensitive search (default: false)',
        default: false,
      },
      contextLines: {
        type: 'number',
        description: 'Number of context lines to show around matches (default: 0)',
        default: 0,
      },
      maxMatches: {
        type: 'number',
        description: 'Maximum number of matches to return per file (default: 100)',
        default: 100,
      },
      filePattern: {
        type: 'string',
        description: 'File name pattern to include (glob pattern)',
      },
      excludePattern: {
        type: 'string',
        description: 'File name pattern to exclude (glob pattern)',
      },
    },
    required: ['pattern', 'path'],
  },
  examples: [
    {
      description: 'Search for function definitions',
      arguments: { pattern: 'function\\s+\\w+', path: './src' },
      expectedOutput: 'List of matching lines with function definitions',
    },
  ],
  riskLevel: 'low',
};

interface MatchResult {
  file: string;
  line: number;
  column: number;
  match: string;
  context?: {
    before: string[];
    after: string[];
  };
}

interface SearchResult {
  pattern: string;
  searchPath: string;
  matches: MatchResult[];
  filesSearched: number;
  totalMatches: number;
  executionTime: number;
}

export async function execute(args: {
  pattern: string;
  path: string;
  recursive?: boolean;
  caseSensitive?: boolean;
  contextLines?: number;
  maxMatches?: number;
  filePattern?: string;
  excludePattern?: string;
}): Promise<SearchResult> {
  const {
    pattern,
    path: searchPath,
    recursive = false,
    caseSensitive = false,
    contextLines = 0,
    maxMatches = 100,
    filePattern,
    excludePattern,
  } = args;

  const startTime = Date.now();
  const matches: MatchResult[] = [];
  let filesSearched = 0;
  let totalMatches = 0;

  try {
    // Create regex pattern
    const flags = caseSensitive ? 'g' : 'gi';
    const regex = new RegExp(pattern, flags);
    
    const absolutePath = path.resolve(searchPath);
    const stats = await fs.stat(absolutePath);
    
    if (stats.isFile()) {
      // Search single file
      if (shouldSearchFile(absolutePath, filePattern, excludePattern)) {
        const fileMatches = await searchInFile(absolutePath, regex, contextLines, maxMatches);
        matches.push(...fileMatches);
        filesSearched = 1;
        totalMatches = fileMatches.length;
      }
    } else if (stats.isDirectory()) {
      // Search directory
      const result = await searchInDirectory(
        absolutePath,
        regex,
        recursive,
        contextLines,
        maxMatches,
        filePattern,
        excludePattern
      );
      matches.push(...result.matches);
      filesSearched = result.filesSearched;
      totalMatches = result.totalMatches;
    } else {
      throw new Error(`Invalid path: ${searchPath}`);
    }

    return {
      pattern,
      searchPath: absolutePath,
      matches,
      filesSearched,
      totalMatches,
      executionTime: Date.now() - startTime,
    };
  } catch (error: any) {
    if (error.code === 'ENOENT') {
      throw new Error(`Path not found: ${searchPath}`);
    } else if (error.code === 'EACCES') {
      throw new Error(`Permission denied: ${searchPath}`);
    } else {
      throw new Error(`Search error: ${error.message}`);
    }
  }
}

async function searchInFile(
  filePath: string,
  regex: RegExp,
  contextLines: number,
  maxMatches: number
): Promise<MatchResult[]> {
  const matches: MatchResult[] = [];
  const lines: string[] = [];
  let lineNumber = 0;

  try {
    // Read file line by line
    const fileStream = createReadStream(filePath);
    const rl = createInterface({
      input: fileStream,
      crlfDelay: Infinity,
    });

    for await (const line of rl) {
      lineNumber++;
      lines.push(line);
      
      // Check if line matches pattern
      const lineMatches = Array.from(line.matchAll(regex));
      
      for (const match of lineMatches) {
        if (matches.length >= maxMatches) {
          break;
        }
        
        const matchResult: MatchResult = {
          file: filePath,
          line: lineNumber,
          column: (match.index || 0) + 1,
          match: match[0],
        };
        
        // Add context if requested
        if (contextLines > 0) {
          const startLine = Math.max(0, lineNumber - contextLines - 1);
          const endLine = Math.min(lines.length - 1, lineNumber + contextLines - 1);
          
          matchResult.context = {
            before: lines.slice(startLine, lineNumber - 1),
            after: [], // Will be filled as we read more lines
          };
        }
        
        matches.push(matchResult);
      }
      
      if (matches.length >= maxMatches) {
        break;
      }
    }
    
    // Fill in the 'after' context for matches
    if (contextLines > 0) {
      for (const match of matches) {
        if (match.context) {
          const startAfter = match.line;
          const endAfter = Math.min(lines.length, match.line + contextLines);
          match.context.after = lines.slice(startAfter, endAfter);
        }
      }
    }
    
    return matches;
  } catch (error: any) {
    if (error.code === 'EISDIR') {
      return []; // Skip directories
    }
    throw new Error(`Error searching file ${filePath}: ${error.message}`);
  }
}

async function searchInDirectory(
  dirPath: string,
  regex: RegExp,
  recursive: boolean,
  contextLines: number,
  maxMatches: number,
  filePattern?: string,
  excludePattern?: string
): Promise<{ matches: MatchResult[]; filesSearched: number; totalMatches: number }> {
  const allMatches: MatchResult[] = [];
  let filesSearched = 0;
  let totalMatches = 0;

  async function searchRecursively(currentDir: string, depth: number = 0): Promise<void> {
    // Prevent infinite recursion
    if (depth > 10) return;
    
    try {
      const entries = await fs.readdir(currentDir, { withFileTypes: true });
      
      for (const entry of entries) {
        if (totalMatches >= maxMatches * 10) {
          // Limit total matches across all files
          break;
        }
        
        const fullPath = path.join(currentDir, entry.name);
        
        if (entry.isDirectory()) {
          if (recursive && !entry.name.startsWith('.')) {
            await searchRecursively(fullPath, depth + 1);
          }
        } else if (entry.isFile()) {
          if (shouldSearchFile(fullPath, filePattern, excludePattern)) {
            try {
              const fileMatches = await searchInFile(fullPath, regex, contextLines, maxMatches);
              allMatches.push(...fileMatches);
              filesSearched++;
              totalMatches += fileMatches.length;
            } catch (error) {
              console.warn(`Skipping file ${fullPath}: ${error}`);
            }
          }
        }
      }
    } catch (error) {
      console.warn(`Error accessing directory ${currentDir}: ${error}`);
    }
  }

  await searchRecursively(dirPath);
  
  return {
    matches: allMatches.slice(0, maxMatches * 10), // Limit total results
    filesSearched,
    totalMatches,
  };
}

function shouldSearchFile(
  filePath: string,
  includePattern?: string,
  excludePattern?: string
): boolean {
  const fileName = path.basename(filePath);
  
  // Skip binary files and common non-text extensions
  const binaryExtensions = ['.exe', '.dll', '.so', '.dylib', '.bin', '.jpg', '.png', '.gif', '.pdf', '.zip', '.tar', '.gz'];
  const ext = path.extname(fileName).toLowerCase();
  if (binaryExtensions.includes(ext)) {
    return false;
  }
  
  // Check include pattern
  if (includePattern) {
    const includeRegex = new RegExp(includePattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
    if (!includeRegex.test(fileName)) {
      return false;
    }
  }
  
  // Check exclude pattern
  if (excludePattern) {
    const excludeRegex = new RegExp(excludePattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
    if (excludeRegex.test(fileName)) {
      return false;
    }
  }
  
  // Skip hidden files and common ignore patterns
  if (fileName.startsWith('.') || fileName.includes('node_modules') || fileName.includes('.git')) {
    return false;
  }
  
  return true;
}

// Helper function for simple text search (non-regex)
export async function searchText(args: {
  text: string;
  path: string;
  recursive?: boolean;
  caseSensitive?: boolean;
}): Promise<SearchResult> {
  const { text, ...otherArgs } = args;
  
  // Escape special regex characters for literal text search
  const escapedText = text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  
  return execute({
    pattern: escapedText,
    ...otherArgs,
  });
}