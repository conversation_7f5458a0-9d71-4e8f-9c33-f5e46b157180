import type { AppSettings } from './types';

// Application constants
export const APP_NAME = 'AI Assistant';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = 'Desktop AI Assistant with Multi-LLM Support';

// Database constants
export const DB_CONSTANTS = {
  DATABASE_NAME: 'ai-assistant.db',
  BACKUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
  CLEANUP_INTERVAL: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
  MAX_CONVERSATION_AGE: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
  MAX_CACHE_SIZE: 100 * 1024 * 1024, // 100MB in bytes
};

// LLM Provider constants
export const LLM_PROVIDERS = {
  OPENAI: 'openai',
  ANTHROPIC: 'anthropic',
  DEEPSEEK: 'deepseek',
} as const;

// Default models for each provider
export const DEFAULT_MODELS = {
  [LLM_PROVIDERS.OPENAI]: {
    'gpt-4': 'GPT-4',
    'gpt-4-turbo': 'GPT-4 Turbo',
    'gpt-3.5-turbo': 'GPT-3.5 Turbo',
  },
  [LLM_PROVIDERS.ANTHROPIC]: {
    'claude-3-opus-20240229': 'Claude 3 Opus',
    'claude-3-sonnet-20240229': 'Claude 3 Sonnet',
    'claude-3-haiku-20240307': 'Claude 3 Haiku',
  },
  [LLM_PROVIDERS.DEEPSEEK]: {
    'deepseek-chat': 'DeepSeek Chat',
    'deepseek-coder': 'DeepSeek Coder',
  },
} as const;

// API endpoints
export const API_ENDPOINTS = {
  [LLM_PROVIDERS.OPENAI]: 'https://api.openai.com/v1',
  [LLM_PROVIDERS.ANTHROPIC]: 'https://api.anthropic.com/v1',
  [LLM_PROVIDERS.DEEPSEEK]: 'https://api.deepseek.com/v1',
} as const;

// Token limits for different models
export const TOKEN_LIMITS = {
  'gpt-4': 8192,
  'gpt-4-turbo': 128000,
  'gpt-3.5-turbo': 4096,
  'claude-3-opus-20240229': 200000,
  'claude-3-sonnet-20240229': 200000,
  'claude-3-haiku-20240307': 200000,
  'deepseek-chat': 32768,
  'deepseek-coder': 32768,
} as const;

// UI constants
export const UI_CONSTANTS = {
  SIDEBAR_WIDTH: 280,
  SIDEBAR_COLLAPSED_WIDTH: 60,
  HEADER_HEIGHT: 60,
  MESSAGE_MAX_WIDTH: 800,
  ANIMATION_DURATION: 200,
  DEBOUNCE_DELAY: 300,
  TOAST_DURATION: 5000,
  TYPING_INDICATOR_DELAY: 1000,
} as const;

// Keyboard shortcuts
export const KEYBOARD_SHORTCUTS = {
  NEW_CONVERSATION: 'Ctrl+N',
  TOGGLE_SIDEBAR: 'Ctrl+B',
  TOGGLE_SETTINGS: 'Ctrl+,',
  FOCUS_INPUT: 'Ctrl+Enter',
  CLOSE_MODAL: 'Escape',
  TOGGLE_FULLSCREEN: 'F11',
  TOGGLE_DEVTOOLS: 'F12',
  RELOAD_APP: 'Ctrl+Shift+R',
} as const;

// Agent execution modes
export const EXECUTION_MODES = {
  CONFIRM: 'confirm',
  YOLO: 'yolo',
} as const;

// Tool categories
export const TOOL_CATEGORIES = {
  FILE_SYSTEM: 'file_system',
  SHELL: 'shell',
  SEARCH: 'search',
  NETWORK: 'network',
  SYSTEM: 'system',
} as const;

// File type mappings
export const FILE_TYPES = {
  TEXT: ['txt', 'md', 'json', 'xml', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf'],
  CODE: ['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'h', 'php', 'rb', 'go', 'rs', 'swift'],
  WEB: ['html', 'css', 'scss', 'sass', 'less'],
  DATA: ['csv', 'tsv', 'json', 'xml', 'sql'],
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2'],
} as const;

// Default application settings
export const DEFAULT_SETTINGS: AppSettings = {
  llm: {
    provider: LLM_PROVIDERS.OPENAI,
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2048,
    topP: 1.0,
    frequencyPenalty: 0.0,
    presencePenalty: 0.0,
    apiKeys: {
      openai: '',
      anthropic: '',
      deepseek: '',
    },
  },
  ui: {
    theme: 'dark',
    sidebarOpen: true,
    fontSize: 'medium',
    fontFamily: 'Inter',
    compactMode: false,
    showLineNumbers: true,
    wordWrap: true,
    animations: true,
  },
  agent: {
    defaultExecutionMode: EXECUTION_MODES.CONFIRM,
    autoSave: true,
    confirmDestructiveActions: true,
    maxConcurrentTools: 3,
    toolTimeout: 30000,
    enableLogging: true,
    logLevel: 'info',
  },
  system: {
    autoUpdate: true,
    telemetry: false,
    crashReporting: true,
    hardwareAcceleration: true,
    startMinimized: false,
    minimizeToTray: true,
    closeToTray: false,
  },
  shortcuts: {
    newConversation: KEYBOARD_SHORTCUTS.NEW_CONVERSATION,
    toggleSidebar: KEYBOARD_SHORTCUTS.TOGGLE_SIDEBAR,
    toggleSettings: KEYBOARD_SHORTCUTS.TOGGLE_SETTINGS,
    focusInput: KEYBOARD_SHORTCUTS.FOCUS_INPUT,
    closeModal: KEYBOARD_SHORTCUTS.CLOSE_MODAL,
  },
  advanced: {
    debugMode: false,
    experimentalFeatures: false,
    customPrompts: [],
    toolWhitelist: [],
    toolBlacklist: [],
    maxHistorySize: 1000,
    contextWindow: 4096,
  },
};

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  API_KEY_MISSING: 'API key is required for the selected provider.',
  API_KEY_INVALID: 'Invalid API key. Please check your credentials.',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
  MODEL_NOT_AVAILABLE: 'Selected model is not available.',
  TOOL_EXECUTION_FAILED: 'Tool execution failed. Please try again.',
  FILE_NOT_FOUND: 'File not found.',
  PERMISSION_DENIED: 'Permission denied.',
  INVALID_INPUT: 'Invalid input provided.',
  UNKNOWN_ERROR: 'An unknown error occurred.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  SETTINGS_SAVED: 'Settings saved successfully.',
  CONVERSATION_CREATED: 'New conversation created.',
  CONVERSATION_DELETED: 'Conversation deleted.',
  FILE_SAVED: 'File saved successfully.',
  TOOL_EXECUTED: 'Tool executed successfully.',
  API_KEY_VALIDATED: 'API key validated successfully.',
} as const;

// Validation rules
export const VALIDATION_RULES = {
  API_KEY_MIN_LENGTH: 10,
  CONVERSATION_TITLE_MAX_LENGTH: 100,
  MESSAGE_MAX_LENGTH: 10000,
  FILENAME_MAX_LENGTH: 255,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_CONCURRENT_REQUESTS: 5,
} as const;

// Feature flags
export const FEATURE_FLAGS = {
  ENABLE_VOICE_INPUT: false,
  ENABLE_VOICE_OUTPUT: false,
  ENABLE_PLUGINS: false,
  ENABLE_CUSTOM_TOOLS: false,
  ENABLE_COLLABORATION: false,
  ENABLE_CLOUD_SYNC: false,
} as const;

// Development constants
export const DEV_CONSTANTS = {
  MOCK_DELAY: 1000,
  DEBUG_LOGGING: true,
  HOT_RELOAD: true,
  DEVTOOLS_ENABLED: true,
} as const;

// Production constants
export const PROD_CONSTANTS = {
  TELEMETRY_ENDPOINT: 'https://telemetry.example.com',
  UPDATE_CHECK_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  CRASH_REPORT_ENDPOINT: 'https://crashes.example.com',
  ANALYTICS_ENDPOINT: 'https://analytics.example.com',
} as const;

// Regular expressions
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/.+/,
  API_KEY: /^[a-zA-Z0-9_-]+$/,
  FILENAME: /^[^<>:"/\\|?*]+$/,
  VERSION: /^\d+\.\d+\.\d+$/,
} as const;

// Color themes
export const COLOR_THEMES = {
  DARK: {
    primary: '#0ea5e9',
    secondary: '#64748b',
    background: '#0f172a',
    surface: '#1e293b',
    text: '#f8fafc',
    textSecondary: '#cbd5e1',
    border: '#334155',
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
  },
  LIGHT: {
    primary: '#0ea5e9',
    secondary: '#64748b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#0f172a',
    textSecondary: '#475569',
    border: '#e2e8f0',
    success: '#22c55e',
    warning: '#f59e0b',
    error: '#ef4444',
  },
} as const;

// Export types for constants
export type LLMProvider = typeof LLM_PROVIDERS[keyof typeof LLM_PROVIDERS];
export type ExecutionMode = typeof EXECUTION_MODES[keyof typeof EXECUTION_MODES];
export type ToolCategory = typeof TOOL_CATEGORIES[keyof typeof TOOL_CATEGORIES];
