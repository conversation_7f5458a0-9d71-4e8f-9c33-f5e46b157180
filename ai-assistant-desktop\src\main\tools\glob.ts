import { glob as globPattern } from 'glob';
import * as path from 'path';
import * as fs from 'fs/promises';
import type { ToolSchema } from '@shared/types';
import { TOOL_CATEGORIES } from '@shared/constants';

export const category = TOOL_CATEGORIES.FILE;

export const schema: ToolSchema = {
  name: 'glob',
  description: 'Find files and directories using glob patterns. Supports wildcards, character classes, and advanced pattern matching.',
  category: TOOL_CATEGORIES.FILE,
  parameters: {
    type: 'object',
    properties: {
      pattern: {
        type: 'string',
        description: 'Glob pattern to match files/directories (e.g., "**/*.ts", "src/**/*.{js,ts}", "*.json")',
      },
      cwd: {
        type: 'string',
        description: 'Working directory to search from (default: current directory)',
      },
      includeDirectories: {
        type: 'boolean',
        description: 'Whether to include directories in results (default: false)',
        default: false,
      },
      followSymlinks: {
        type: 'boolean',
        description: 'Whether to follow symbolic links (default: false)',
        default: false,
      },
      maxResults: {
        type: 'number',
        description: 'Maximum number of results to return (default: 1000)',
        default: 1000,
      },
      ignorePatterns: {
        type: 'array',
        description: 'Array of patterns to ignore (e.g., ["node_modules/**", "*.log"])',
        items: { type: 'string' },
      },
    },
    required: ['pattern'],
  },
  examples: [
    {
      description: 'Find all TypeScript files',
      arguments: { pattern: '**/*.ts' },
      expectedOutput: 'List of TypeScript files',
    },
  ],
  riskLevel: 'low',
};

export async function execute(args: {
  pattern: string;
  cwd?: string;
  includeDirectories?: boolean;
  followSymlinks?: boolean;
  maxResults?: number;
  ignorePatterns?: string[];
}): Promise<{
  success: boolean;
  message: string;
  results: {
    files: Array<{
      path: string;
      relativePath: string;
      isDirectory: boolean;
      size?: number;
      modified?: string;
    }>;
    totalFound: number;
    pattern: string;
    searchDirectory: string;
  };
  error?: string;
}> {
  const {
    pattern,
    cwd = process.cwd(),
    includeDirectories = false,
    followSymlinks = false,
    maxResults = 1000,
    ignorePatterns = [],
  } = args;

  try {
    // Resolve working directory
    const searchDirectory = path.resolve(cwd);

    // Validate search directory exists
    try {
      await fs.access(searchDirectory);
    } catch {
      return {
        success: false,
        message: `Search directory not found: ${cwd}`,
        results: {
          files: [],
          totalFound: 0,
          pattern,
          searchDirectory,
        },
        error: 'Directory not found',
      };
    }

    // Configure glob options
    const globOptions: any = {
      cwd: searchDirectory,
      absolute: true,
      followSymbolicLinks: followSymlinks,
      ignore: [
        'node_modules/**',
        '.git/**',
        '.vscode/**',
        'dist/**',
        'build/**',
        '*.log',
        '.DS_Store',
        'Thumbs.db',
        ...ignorePatterns,
      ],
    };

    // Execute glob search
    const matches = await globPattern(pattern, globOptions);

    // Process results
    const files: Array<{
      path: string;
      relativePath: string;
      isDirectory: boolean;
      size?: number;
      modified?: string;
    }> = [];

    let processedCount = 0;
    for (const match of matches) {
      if (processedCount >= maxResults) break;

      try {
        const stats = await fs.stat(match);
        const isDirectory = stats.isDirectory();

        // Skip directories if not requested
        if (isDirectory && !includeDirectories) {
          continue;
        }

        const relativePath = path.relative(searchDirectory, match);

        files.push({
          path: match,
          relativePath,
          isDirectory,
          size: isDirectory ? undefined : stats.size,
          modified: stats.mtime.toISOString(),
        });

        processedCount++;
      } catch (error) {
        // Skip files that can't be accessed
        console.warn(`Could not access file: ${match}`, error);
        continue;
      }
    }

    // Sort results: directories first, then files, both alphabetically
    files.sort((a, b) => {
      if (a.isDirectory && !b.isDirectory) return -1;
      if (!a.isDirectory && b.isDirectory) return 1;
      return a.relativePath.localeCompare(b.relativePath);
    });

    const totalFound = matches.length;
    const message = `Found ${totalFound} matches for pattern "${pattern}"${
      totalFound > maxResults ? ` (showing first ${maxResults})` : ''
    }`;

    return {
      success: true,
      message,
      results: {
        files,
        totalFound,
        pattern,
        searchDirectory,
      },
    };

  } catch (error: any) {
    return {
      success: false,
      message: `Glob search failed: ${error.message}`,
      results: {
        files: [],
        totalFound: 0,
        pattern,
        searchDirectory: path.resolve(cwd || process.cwd()),
      },
      error: error.message,
    };
  }
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`;
}

// Helper function to get file extension
function getFileExtension(filePath: string): string {
  return path.extname(filePath).toLowerCase();
}

// Helper function to categorize files
function categorizeFile(filePath: string): string {
  const ext = getFileExtension(filePath);
  
  const categories: Record<string, string[]> = {
    'Code': ['.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.cs', '.php', '.rb', '.go', '.rs'],
    'Web': ['.html', '.css', '.scss', '.sass', '.less'],
    'Config': ['.json', '.yaml', '.yml', '.toml', '.ini', '.conf', '.config'],
    'Documentation': ['.md', '.txt', '.rst', '.adoc'],
    'Image': ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.ico'],
    'Archive': ['.zip', '.tar', '.gz', '.rar', '.7z'],
  };

  for (const [category, extensions] of Object.entries(categories)) {
    if (extensions.includes(ext)) {
      return category;
    }
  }

  return 'Other';
}
