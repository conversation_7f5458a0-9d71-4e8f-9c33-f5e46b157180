import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ChatInterface from '../chat/ChatInterface';
import { useAppStore } from '@renderer/store/appStore';
import type { Conversation, Message } from '@shared/types';

// Mock the store
jest.mock('@renderer/store/appStore');
const mockUseAppStore = useAppStore as jest.MockedFunction<typeof useAppStore>;

// Mock child components
jest.mock('../chat/MessageBubble', () => {
  return function MockMessageBubble({ message }: { message: Message }) {
    return <div data-testid="message-bubble">{message.content}</div>;
  };
});

jest.mock('../chat/ChatInput', () => {
  return function MockChatInput({ onSendMessage }: { onSendMessage: (content: string) => void }) {
    return (
      <div data-testid="chat-input">
        <button onClick={() => onSendMessage('test message')}>Send</button>
      </div>
    );
  };
});

jest.mock('../chat/PlanConfirmation', () => {
  return function MockPlanConfirmation() {
    return <div data-testid="plan-confirmation">Plan Confirmation</div>;
  };
});

jest.mock('../chat/StreamingMessage', () => {
  return function MockStreamingMessage({ content }: { content: string }) {
    return <div data-testid="streaming-message">{content}</div>;
  };
});

describe('ChatInterface', () => {
  const mockConversation: Conversation = {
    id: 'conv-1',
    title: 'Test Conversation',
    messages: [
      {
        id: 'msg-1',
        role: 'user',
        content: 'Hello',
        timestamp: new Date().toISOString(),
      },
      {
        id: 'msg-2',
        role: 'assistant',
        content: 'Hi there! How can I help you?',
        timestamp: new Date().toISOString(),
      },
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const defaultStoreState = {
    currentConversation: mockConversation,
    currentPlan: null,
    toolResults: [],
    isStreaming: false,
    streamingMessage: '',
    agentState: null,
  };

  beforeEach(() => {
    mockUseAppStore.mockReturnValue(defaultStoreState as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders welcome screen when no conversation is selected', () => {
    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      currentConversation: null,
    } as any);

    render(<ChatInterface />);

    expect(screen.getByText('Welcome to AI Assistant')).toBeInTheDocument();
    expect(screen.getByText(/Start a new conversation/)).toBeInTheDocument();
  });

  it('renders conversation messages when conversation is selected', () => {
    render(<ChatInterface />);

    const messageBubbles = screen.getAllByTestId('message-bubble');
    expect(messageBubbles).toHaveLength(2);
    expect(messageBubbles[0]).toHaveTextContent('Hello');
    expect(messageBubbles[1]).toHaveTextContent('Hi there! How can I help you?');
  });

  it('renders chat input when conversation is selected', () => {
    render(<ChatInterface />);

    expect(screen.getByTestId('chat-input')).toBeInTheDocument();
  });

  it('renders streaming message when streaming is active', () => {
    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      isStreaming: true,
      streamingMessage: 'Streaming response...',
    } as any);

    render(<ChatInterface />);

    expect(screen.getByTestId('streaming-message')).toBeInTheDocument();
    expect(screen.getByText('Streaming response...')).toBeInTheDocument();
  });

  it('renders plan confirmation when plan is available', () => {
    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      currentPlan: {
        id: 'plan-1',
        title: 'Test Plan',
        description: 'A test plan',
        requires_confirmation: true,
        steps: [],
      },
    } as any);

    render(<ChatInterface />);

    expect(screen.getByTestId('plan-confirmation')).toBeInTheDocument();
  });

  it('scrolls to bottom when new messages arrive', async () => {
    const scrollIntoViewMock = jest.fn();
    Element.prototype.scrollIntoView = scrollIntoViewMock;

    const { rerender } = render(<ChatInterface />);

    // Add a new message
    const updatedConversation = {
      ...mockConversation,
      messages: [
        ...mockConversation.messages,
        {
          id: 'msg-3',
          role: 'user' as const,
          content: 'New message',
          timestamp: new Date().toISOString(),
        },
      ],
    };

    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      currentConversation: updatedConversation,
    } as any);

    rerender(<ChatInterface />);

    await waitFor(() => {
      expect(scrollIntoViewMock).toHaveBeenCalledWith({ behavior: 'smooth' });
    });
  });

  it('handles empty conversation gracefully', () => {
    const emptyConversation = {
      ...mockConversation,
      messages: [],
    };

    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      currentConversation: emptyConversation,
    } as any);

    render(<ChatInterface />);

    expect(screen.getByTestId('chat-input')).toBeInTheDocument();
    expect(screen.queryByTestId('message-bubble')).not.toBeInTheDocument();
  });

  it('displays tool results when available', () => {
    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      toolResults: [
        {
          success: true,
          output: 'Tool executed successfully',
          data: { result: 'test' },
        },
      ],
    } as any);

    render(<ChatInterface />);

    // Tool results would be displayed in ToolResultDisplay component
    // This test assumes the component renders tool results
    expect(screen.getByTestId('chat-input')).toBeInTheDocument();
  });

  it('handles agent state changes', () => {
    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      agentState: {
        isProcessing: true,
        isWaitingForConfirmation: false,
        currentStep: 'analyzing',
      },
    } as any);

    render(<ChatInterface />);

    // The component should handle agent state appropriately
    expect(screen.getByTestId('chat-input')).toBeInTheDocument();
  });

  it('maintains scroll position during streaming', async () => {
    const scrollIntoViewMock = jest.fn();
    Element.prototype.scrollIntoView = scrollIntoViewMock;

    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      isStreaming: true,
      streamingMessage: 'Initial streaming...',
    } as any);

    const { rerender } = render(<ChatInterface />);

    // Update streaming message
    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      isStreaming: true,
      streamingMessage: 'Updated streaming message...',
    } as any);

    rerender(<ChatInterface />);

    await waitFor(() => {
      expect(scrollIntoViewMock).toHaveBeenCalled();
    });
  });

  it('renders correctly with all features enabled', () => {
    mockUseAppStore.mockReturnValue({
      ...defaultStoreState,
      isStreaming: true,
      streamingMessage: 'Streaming...',
      currentPlan: {
        id: 'plan-1',
        title: 'Test Plan',
        description: 'A test plan',
        requires_confirmation: true,
        steps: [],
      },
      toolResults: [
        {
          success: true,
          output: 'Tool result',
          data: {},
        },
      ],
      agentState: {
        isProcessing: true,
        isWaitingForConfirmation: true,
        currentStep: 'executing',
      },
    } as any);

    render(<ChatInterface />);

    expect(screen.getByTestId('chat-input')).toBeInTheDocument();
    expect(screen.getByTestId('streaming-message')).toBeInTheDocument();
    expect(screen.getByTestId('plan-confirmation')).toBeInTheDocument();
    expect(screen.getAllByTestId('message-bubble')).toHaveLength(2);
  });
});

// Mock useCurrentMessages selector
jest.mock('@renderer/store/appStore', () => ({
  useAppStore: jest.fn(),
  useCurrentMessages: jest.fn(() => [
    {
      id: 'msg-1',
      role: 'user',
      content: 'Hello',
      timestamp: new Date().toISOString(),
    },
    {
      id: 'msg-2',
      role: 'assistant',
      content: 'Hi there! How can I help you?',
      timestamp: new Date().toISOString(),
    },
  ]),
}));
